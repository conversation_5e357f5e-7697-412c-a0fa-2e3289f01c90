<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone Casting Pro - Mobile App</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            padding: 0;
            overflow-x: hidden;
        }

        .mobile-container {
            max-width: 400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            min-height: 100vh;
            color: #333;
            position: relative;
        }

        .status-bar {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .app-header {
            background: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .app-header h1 {
            color: #667eea;
            font-size: 1.8rem;
            margin-bottom: 5px;
        }

        .app-header p {
            color: #666;
            font-size: 0.9rem;
        }

        .connection-card {
            margin: 20px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #4CAF50;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        .connection-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .info-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .info-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
        }

        .info-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 2px;
        }

        .quick-actions {
            margin: 20px;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .action-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .action-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .action-card.active {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
        }

        .action-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #667eea;
        }

        .action-card.active .action-icon {
            color: white;
        }

        .action-title {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .action-desc {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .permissions-section {
            margin: 20px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .permission-item:last-child {
            border-bottom: none;
        }

        .permission-info {
            flex: 1;
        }

        .permission-name {
            font-weight: 500;
            margin-bottom: 2px;
        }

        .permission-desc {
            font-size: 0.8rem;
            color: #666;
        }

        .permission-toggle {
            width: 50px;
            height: 25px;
            background: #ccc;
            border-radius: 12px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .permission-toggle.active {
            background: linear-gradient(145deg, #4CAF50, #45a049);
        }

        .permission-toggle::after {
            content: '';
            position: absolute;
            width: 21px;
            height: 21px;
            border-radius: 50%;
            background: white;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .permission-toggle.active::after {
            transform: translateX(25px);
        }

        .qr-section {
            margin: 20px;
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .qr-code {
            width: 150px;
            height: 150px;
            background: #f8f9fa;
            border: 2px dashed #667eea;
            border-radius: 10px;
            margin: 15px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #667eea;
        }

        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 400px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 8px;
            min-width: 60px;
        }

        .nav-item.active {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .nav-icon {
            font-size: 1.2rem;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 0.7rem;
            font-weight: 500;
        }

        .floating-btn {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(145deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            border: none;
        }

        .floating-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .content-section {
            padding-bottom: 100px;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .notification {
            position: fixed;
            top: 60px;
            left: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            transform: translateY(-100px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .notification.show {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
                <span>85%</span>
            </div>
        </div>

        <!-- App Header -->
        <div class="app-header">
            <h1><i class="fas fa-mobile-alt"></i> Phone Casting</h1>
            <p>Share your screen wirelessly</p>
        </div>

        <div class="content-section">
            <!-- Connection Status -->
            <div class="connection-card">
                <div class="connection-status">
                    <div class="status-dot"></div>
                    <div>
                        <h3>Connected to Laptop</h3>
                        <p>Dell XPS 15 - 192.168.1.100</p>
                    </div>
                </div>
                <div class="connection-info">
                    <div class="info-item">
                        <div class="info-value">Wi-Fi</div>
                        <div class="info-label">Connection</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">15ms</div>
                        <div class="info-label">Latency</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">720p</div>
                        <div class="info-label">Quality</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value">30fps</div>
                        <div class="info-label">Frame Rate</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <div class="section-title">
                    <i class="fas fa-bolt"></i>
                    Quick Actions
                </div>
                <div class="action-grid">
                    <div class="action-card active" data-action="screen">
                        <div class="action-icon">📱</div>
                        <div class="action-title">Screen Share</div>
                        <div class="action-desc">Share your screen</div>
                    </div>
                    <div class="action-card" data-action="files">
                        <div class="action-icon">📁</div>
                        <div class="action-title">File Transfer</div>
                        <div class="action-desc">Send/receive files</div>
                    </div>
                    <div class="action-card" data-action="clipboard">
                        <div class="action-icon">📋</div>
                        <div class="action-title">Clipboard Sync</div>
                        <div class="action-desc">Sync clipboard</div>
                    </div>
                    <div class="action-card" data-action="input">
                        <div class="action-icon">🖱️</div>
                        <div class="action-title">Remote Input</div>
                        <div class="action-desc">Control laptop</div>
                    </div>
                </div>
            </div>

            <!-- Permissions -->
            <div class="permissions-section">
                <div class="section-title">
                    <i class="fas fa-shield-alt"></i>
                    Permissions
                </div>
                <div class="permission-item">
                    <div class="permission-info">
                        <div class="permission-name">Screen Recording</div>
                        <div class="permission-desc">Allow screen capture and sharing</div>
                    </div>
                    <div class="permission-toggle active" data-permission="screen"></div>
                </div>
                <div class="permission-item">
                    <div class="permission-info">
                        <div class="permission-name">File Access</div>
                        <div class="permission-desc">Access files for transfer</div>
                    </div>
                    <div class="permission-toggle active" data-permission="files"></div>
                </div>
                <div class="permission-item">
                    <div class="permission-info">
                        <div class="permission-name">Network Access</div>
                        <div class="permission-desc">Connect to other devices</div>
                    </div>
                    <div class="permission-toggle active" data-permission="network"></div>
                </div>
                <div class="permission-item">
                    <div class="permission-info">
                        <div class="permission-name">Accessibility Service</div>
                        <div class="permission-desc">Enable remote input control</div>
                    </div>
                    <div class="permission-toggle" data-permission="accessibility"></div>
                </div>
            </div>

            <!-- QR Code Section -->
            <div class="qr-section">
                <div class="section-title">
                    <i class="fas fa-qrcode"></i>
                    Quick Connect
                </div>
                <p>Scan this QR code from your laptop to connect instantly</p>
                <div class="qr-code">
                    <i class="fas fa-qrcode"></i>
                </div>
                <button class="btn btn-primary" style="background: linear-gradient(145deg, #667eea, #764ba2); color: white; border: none; padding: 10px 20px; border-radius: 20px; font-weight: bold;">
                    <i class="fas fa-sync-alt"></i> Generate New Code
                </button>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-item active" data-tab="home">
                <div class="nav-icon"><i class="fas fa-home"></i></div>
                <div class="nav-label">Home</div>
            </div>
            <div class="nav-item" data-tab="devices">
                <div class="nav-icon"><i class="fas fa-laptop"></i></div>
                <div class="nav-label">Devices</div>
            </div>
            <div class="nav-item" data-tab="files">
                <div class="nav-icon"><i class="fas fa-folder"></i></div>
                <div class="nav-label">Files</div>
            </div>
            <div class="nav-item" data-tab="settings">
                <div class="nav-icon"><i class="fas fa-cog"></i></div>
                <div class="nav-label">Settings</div>
            </div>
        </div>

        <!-- Floating Action Button -->
        <button class="floating-btn" id="share-btn">
            <i class="fas fa-share-alt"></i>
        </button>

        <!-- Notification -->
        <div class="notification" id="notification">
            <i class="fas fa-check-circle"></i> Screen sharing started successfully!
        </div>
    </div>

    <script>
        // Action cards functionality
        document.querySelectorAll('.action-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove active class from all cards
                document.querySelectorAll('.action-card').forEach(c => c.classList.remove('active'));
                // Add active class to clicked card
                this.classList.add('active');
                
                const action = this.dataset.action;
                showNotification(`${this.querySelector('.action-title').textContent} activated!`);
            });
        });

        // Permission toggles
        document.querySelectorAll('.permission-toggle').forEach(toggle => {
            toggle.addEventListener('click', function() {
                this.classList.toggle('active');
                const permission = this.dataset.permission;
                const isActive = this.classList.contains('active');
                showNotification(`${permission} permission ${isActive ? 'granted' : 'revoked'}`);
            });
        });

        // Bottom navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.nav-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // Floating action button
        document.getElementById('share-btn').addEventListener('click', function() {
            showNotification('Starting screen share...');
            setTimeout(() => {
                showNotification('Screen sharing started successfully!');
            }, 2000);
        });

        // Notification system
        function showNotification(message) {
            const notification = document.getElementById('notification');
            notification.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Simulate real-time updates
        setInterval(() => {
            const latencyElement = document.querySelector('.info-item .info-value');
            if (latencyElement && latencyElement.textContent.includes('ms')) {
                const newLatency = Math.floor(Math.random() * 10) + 10;
                latencyElement.textContent = newLatency + 'ms';
            }
        }, 5000);

        // Initialize with welcome message
        setTimeout(() => {
            showNotification('Welcome to Phone Casting Pro!');
        }, 1000);
    </script>
</body>
</html>
