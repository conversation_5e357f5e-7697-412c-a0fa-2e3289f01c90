✅ Supported Modes
Mode	Description
📶 Wi-Fi	Both devices connected to the same network or via Wi-Fi Direct
🔗 Bluetooth	Laptop and phone pair via Bluetooth for control, file transfer, or signaling
🔌 USB (ADB)	USB debugging mode for low-latency access and command/control
🔁 USB-Free Mode	Pure wireless operation via Bluetooth/Wi-Fi without needing any cables

🔄 Updated System Architecture
plaintext
Copy
Edit
+-------------------+       WiFi / BT / USB       +-------------------+
|   Android Phone   | <-------------------------> |    Laptop App     |
|-------------------|                            |-------------------|
| - MediaProjection |                            | - Stream Receiver |
| - InputService    |                            | - Input Injector  |
| - BluetoothServer |                            | - Device Connector|
| - WiFi Server     |                            | - UI/UX Panel     |
+-------------------+                            +-------------------+
🔧 Updated Project Structure
plaintext
Copy
Edit
PhoneCloneApp/
├── android-client/
│   ├── connectivity/
│   │   ├── WifiServer.kt        # Wi-Fi Socket Server
│   │   ├── BluetoothServer.kt   # Bluetooth Classic/LE
│   │   └── UsbService.kt        # ADB/USB transport layer
│   └── permissions/
│       └── Wifi_BT_USB_PermissionManager.kt
│
├── laptop-client/
│   ├── connectivity/
│   │   ├── WifiClient.ts        # Wi-Fi client with discovery
│   │   ├── BluetoothClient.ts   # Node Bluetooth client module
│   │   └── UsbClient.ts         # ADB bridge (using adbkit or subprocess)
│
└── shared/
    └── connection_handler/
        └── ConnectionProtocol.ts / .kt  # Defines handshake, authentication, fallback
🔄 Connection Mode Implementation Details
1. 📶 Wi-Fi Mode
Phone runs a lightweight TCP/WebSocket server.

Laptop connects using the IP address and port (with discovery).

Use mDNS or QR scan pairing for convenience.

2. 🔗 Bluetooth Mode
Use Bluetooth Classic SPP (Serial Port Profile) or BLE characteristics.

Use for:

Low-data input control

Simple streaming

Notifications sync

For streaming, fallback to Wi-Fi if bandwidth insufficient.

3. 🔌 USB Mode (ADB)
Utilize ADB shell to capture and control phone.

Use scrcpy-like backend for high performance.

4. 🔁 Hybrid USB-Free Mode
Laptop connects to phone’s Wi-Fi hotspot.

Uses Wi-Fi Direct or P2P TCP streaming.

⚙️ Laptop App – Connection UI Panel Example
plaintext
Copy
Edit
┌────────────────────────────┐
│ 🔌 Choose Connection Type: │
│                            │
│ [1] Wi-Fi (Auto Discover)  │
│ [2] Bluetooth              │
│ [3] USB (ADB)              │
│ [4] QR Scan (Hotspot Pair) │
└────────────────────────────┘
On successful connection:

plaintext
Copy
Edit
📱 Connected to: OnePlus Nord CE
Mode: Wi-Fi Direct
Status: Stream Live 🎥
Input: Enabled 🖱️
🧠 Intelligent Fallback Logic (Optional)
If one mode fails:

Try Wi-Fi → if weak signal →

Try Bluetooth → if latency high →

Recommend USB or show QR for Hotspot.

📋 Permissions Required (Android)
INTERNET, ACCESS_WIFI_STATE, CHANGE_WIFI_STATE

BLUETOOTH_CONNECT, BLUETOOTH_ADMIN, BLUETOOTH

ACCESS_NETWORK_STATE

SYSTEM_ALERT_WINDOW, FOREGROUND_SERVICE

MEDIA_PROJECTION and AccessibilityService permissions

✅ Features Summary
Feature	Supported
Screen mirroring (live)	✅
Mouse + keyboard input	✅
Connect via Wi-Fi	✅
Connect via Bluetooth	✅
Connect via USB (ADB)	✅
QR/Hotspot/USB-Free	✅
Clipboard/file transfer	🔄 WIP
Touchpad Mode	✅
Background sync	✅

Would you like me to now:

Generate a Bluetooth + Wi-Fi connection boilerplate for Android?

Create the Electron app with Wi-Fi/BT/USB connectivity panel?

Provide a UI/UX wireframe for the desktop and mobile apps?