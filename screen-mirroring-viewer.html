<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone Casting Pro - Screen Mirror</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: white;
            overflow: hidden;
        }

        .mirror-container {
            display: flex;
            height: 100vh;
            position: relative;
        }

        .phone-screen {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            position: relative;
        }

        .phone-frame {
            width: 300px;
            height: 600px;
            background: #000;
            border-radius: 25px;
            padding: 20px;
            box-shadow: 0 0 50px rgba(0,0,0,0.8);
            position: relative;
            border: 3px solid #333;
        }

        .phone-frame.fullscreen {
            width: 100%;
            height: 100%;
            border-radius: 0;
            padding: 0;
            border: none;
        }

        .screen-content {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .phone-frame.fullscreen .screen-content {
            border-radius: 0;
        }

        .demo-content {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
            padding: 20px;
        }

        .demo-content h2 {
            font-size: 2rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .demo-content p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .demo-apps {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .demo-app {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .demo-app:hover {
            transform: scale(1.1);
        }

        .app-phone { background: #4CAF50; }
        .app-message { background: #2196F3; }
        .app-camera { background: #FF9800; }
        .app-gallery { background: #E91E63; }
        .app-music { background: #9C27B0; }
        .app-maps { background: #4CAF50; }
        .app-weather { background: #03A9F4; }
        .app-settings { background: #607D8B; }

        .control-panel {
            width: 350px;
            background: rgba(30, 30, 30, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            overflow-y: auto;
            border-left: 1px solid #444;
        }

        .control-section {
            margin-bottom: 25px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #667eea;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-buttons {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .control-btn {
            padding: 10px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            font-size: 0.9rem;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .control-btn.active {
            background: linear-gradient(145deg, #4CAF50, #45a049);
        }

        .control-btn.recording {
            background: linear-gradient(145deg, #f44336, #d32f2f);
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .quality-controls {
            margin-bottom: 15px;
        }

        .quality-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .quality-label {
            font-size: 0.9rem;
            color: #ccc;
        }

        .quality-value {
            color: #667eea;
            font-weight: bold;
        }

        .slider {
            width: 100%;
            height: 4px;
            border-radius: 2px;
            background: #444;
            outline: none;
            -webkit-appearance: none;
            margin: 5px 0;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: linear-gradient(145deg, #667eea, #764ba2);
            cursor: pointer;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: 6px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #ccc;
            margin-top: 2px;
        }

        .recording-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            display: none;
            animation: pulse 1s infinite;
        }

        .recording-indicator.active {
            display: block;
        }

        .screenshot-flash {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.1s ease;
        }

        .screenshot-flash.active {
            opacity: 0.8;
        }

        .toolbar {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 10px 20px;
            display: flex;
            gap: 15px;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .phone-screen:hover .toolbar {
            opacity: 1;
        }

        .toolbar-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .toolbar-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .connection-status {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(76, 175, 80, 0.9);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: white;
            animation: pulse 2s infinite;
        }

        @media (max-width: 768px) {
            .mirror-container {
                flex-direction: column;
            }
            
            .control-panel {
                width: 100%;
                height: 200px;
                border-left: none;
                border-top: 1px solid #444;
            }
            
            .phone-frame {
                width: 250px;
                height: 500px;
            }
        }
    </style>
</head>
<body>
    <div class="mirror-container">
        <div class="phone-screen">
            <div class="connection-status">
                <div class="status-dot"></div>
                <span>OnePlus Nord CE - Connected</span>
            </div>
            
            <div class="recording-indicator" id="recording-indicator">
                <i class="fas fa-circle"></i> REC
            </div>
            
            <div class="phone-frame" id="phone-frame">
                <div class="screen-content" id="screen-content">
                    <div class="demo-content">
                        <h2>📱 Phone Screen</h2>
                        <p>Live mirroring from your device</p>
                        <div class="demo-apps">
                            <div class="demo-app app-phone"><i class="fas fa-phone"></i></div>
                            <div class="demo-app app-message"><i class="fas fa-comment"></i></div>
                            <div class="demo-app app-camera"><i class="fas fa-camera"></i></div>
                            <div class="demo-app app-gallery"><i class="fas fa-images"></i></div>
                            <div class="demo-app app-music"><i class="fas fa-music"></i></div>
                            <div class="demo-app app-maps"><i class="fas fa-map"></i></div>
                            <div class="demo-app app-weather"><i class="fas fa-cloud-sun"></i></div>
                            <div class="demo-app app-settings"><i class="fas fa-cog"></i></div>
                        </div>
                    </div>
                    <div class="screenshot-flash" id="screenshot-flash"></div>
                </div>
            </div>
            
            <div class="toolbar">
                <button class="toolbar-btn" id="fullscreen-btn" title="Fullscreen">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="toolbar-btn" id="screenshot-btn" title="Screenshot">
                    <i class="fas fa-camera"></i>
                </button>
                <button class="toolbar-btn" id="record-btn" title="Record">
                    <i class="fas fa-video"></i>
                </button>
                <button class="toolbar-btn" id="rotate-btn" title="Rotate">
                    <i class="fas fa-redo"></i>
                </button>
            </div>
        </div>
        
        <div class="control-panel">
            <div class="control-section">
                <div class="section-title">
                    <i class="fas fa-play-circle"></i>
                    Stream Controls
                </div>
                <div class="control-buttons">
                    <button class="control-btn active" id="stream-btn">
                        <i class="fas fa-play"></i> Streaming
                    </button>
                    <button class="control-btn" id="pause-btn">
                        <i class="fas fa-pause"></i> Pause
                    </button>
                    <button class="control-btn" id="record-control-btn">
                        <i class="fas fa-video"></i> Record
                    </button>
                    <button class="control-btn" id="screenshot-control-btn">
                        <i class="fas fa-camera"></i> Screenshot
                    </button>
                </div>
            </div>
            
            <div class="control-section">
                <div class="section-title">
                    <i class="fas fa-sliders-h"></i>
                    Quality Settings
                </div>
                <div class="quality-controls">
                    <div class="quality-item">
                        <span class="quality-label">Resolution</span>
                        <span class="quality-value" id="resolution-value">720p</span>
                    </div>
                    <input type="range" class="slider" id="resolution-slider" min="1" max="3" value="2">
                    
                    <div class="quality-item">
                        <span class="quality-label">Frame Rate</span>
                        <span class="quality-value" id="framerate-value">30fps</span>
                    </div>
                    <input type="range" class="slider" id="framerate-slider" min="15" max="60" value="30">
                    
                    <div class="quality-item">
                        <span class="quality-label">Bitrate</span>
                        <span class="quality-value" id="bitrate-value">8Mbps</span>
                    </div>
                    <input type="range" class="slider" id="bitrate-slider" min="1" max="20" value="8">
                </div>
            </div>
            
            <div class="control-section">
                <div class="section-title">
                    <i class="fas fa-chart-line"></i>
                    Performance Stats
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="latency-stat">12ms</div>
                        <div class="stat-label">Latency</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="fps-stat">30fps</div>
                        <div class="stat-label">Current FPS</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="bandwidth-stat">8.2MB/s</div>
                        <div class="stat-label">Bandwidth</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="quality-stat">98%</div>
                        <div class="stat-label">Quality</div>
                    </div>
                </div>
            </div>
            
            <div class="control-section">
                <div class="section-title">
                    <i class="fas fa-mouse"></i>
                    Input Controls
                </div>
                <div class="control-buttons">
                    <button class="control-btn active" id="mouse-btn">
                        <i class="fas fa-mouse"></i> Mouse
                    </button>
                    <button class="control-btn active" id="keyboard-btn">
                        <i class="fas fa-keyboard"></i> Keyboard
                    </button>
                    <button class="control-btn" id="gamepad-btn">
                        <i class="fas fa-gamepad"></i> Gamepad
                    </button>
                    <button class="control-btn active" id="touch-btn">
                        <i class="fas fa-hand-pointer"></i> Touch
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isFullscreen = false;
        let isRecording = false;
        let recordingTime = 0;
        let recordingInterval;

        // Fullscreen functionality
        document.getElementById('fullscreen-btn').addEventListener('click', toggleFullscreen);
        document.getElementById('phone-frame').addEventListener('dblclick', toggleFullscreen);

        function toggleFullscreen() {
            const phoneFrame = document.getElementById('phone-frame');
            const fullscreenBtn = document.getElementById('fullscreen-btn');
            const controlPanel = document.querySelector('.control-panel');
            
            isFullscreen = !isFullscreen;
            
            if (isFullscreen) {
                phoneFrame.classList.add('fullscreen');
                controlPanel.style.display = 'none';
                fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
                fullscreenBtn.title = 'Exit Fullscreen';
            } else {
                phoneFrame.classList.remove('fullscreen');
                controlPanel.style.display = 'block';
                fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
                fullscreenBtn.title = 'Fullscreen';
            }
        }

        // Screenshot functionality
        document.getElementById('screenshot-btn').addEventListener('click', takeScreenshot);
        document.getElementById('screenshot-control-btn').addEventListener('click', takeScreenshot);

        function takeScreenshot() {
            const flash = document.getElementById('screenshot-flash');
            flash.classList.add('active');
            
            setTimeout(() => {
                flash.classList.remove('active');
                // Simulate screenshot save
                const timestamp = new Date().toLocaleString();
                alert(`Screenshot saved!\nFilename: screenshot_${Date.now()}.png\nTime: ${timestamp}`);
            }, 100);
        }

        // Recording functionality
        document.getElementById('record-btn').addEventListener('click', toggleRecording);
        document.getElementById('record-control-btn').addEventListener('click', toggleRecording);

        function toggleRecording() {
            const recordBtn = document.getElementById('record-btn');
            const recordControlBtn = document.getElementById('record-control-btn');
            const recordingIndicator = document.getElementById('recording-indicator');
            
            isRecording = !isRecording;
            
            if (isRecording) {
                recordBtn.innerHTML = '<i class="fas fa-stop"></i>';
                recordControlBtn.innerHTML = '<i class="fas fa-stop"></i> Stop';
                recordControlBtn.classList.add('recording');
                recordingIndicator.classList.add('active');
                
                recordingTime = 0;
                recordingInterval = setInterval(() => {
                    recordingTime++;
                    const minutes = Math.floor(recordingTime / 60);
                    const seconds = recordingTime % 60;
                    recordingIndicator.innerHTML = `<i class="fas fa-circle"></i> REC ${minutes}:${seconds.toString().padStart(2, '0')}`;
                }, 1000);
            } else {
                recordBtn.innerHTML = '<i class="fas fa-video"></i>';
                recordControlBtn.innerHTML = '<i class="fas fa-video"></i> Record';
                recordControlBtn.classList.remove('recording');
                recordingIndicator.classList.remove('active');
                
                clearInterval(recordingInterval);
                const duration = `${Math.floor(recordingTime / 60)}:${(recordingTime % 60).toString().padStart(2, '0')}`;
                alert(`Recording saved!\nDuration: ${duration}\nFilename: recording_${Date.now()}.mp4`);
            }
        }

        // Quality sliders
        const resolutions = ['480p', '720p', '1080p'];
        document.getElementById('resolution-slider').addEventListener('input', function() {
            document.getElementById('resolution-value').textContent = resolutions[this.value - 1];
        });

        document.getElementById('framerate-slider').addEventListener('input', function() {
            document.getElementById('framerate-value').textContent = this.value + 'fps';
            document.getElementById('fps-stat').textContent = this.value + 'fps';
        });

        document.getElementById('bitrate-slider').addEventListener('input', function() {
            document.getElementById('bitrate-value').textContent = this.value + 'Mbps';
            document.getElementById('bandwidth-stat').textContent = (this.value * 1.024).toFixed(1) + 'MB/s';
        });

        // Stream controls
        document.getElementById('stream-btn').addEventListener('click', function() {
            this.classList.toggle('active');
            if (this.classList.contains('active')) {
                this.innerHTML = '<i class="fas fa-play"></i> Streaming';
            } else {
                this.innerHTML = '<i class="fas fa-stop"></i> Stopped';
            }
        });

        document.getElementById('pause-btn').addEventListener('click', function() {
            this.classList.toggle('active');
            if (this.classList.contains('active')) {
                this.innerHTML = '<i class="fas fa-play"></i> Resume';
            } else {
                this.innerHTML = '<i class="fas fa-pause"></i> Pause';
            }
        });

        // Input controls
        document.querySelectorAll('#mouse-btn, #keyboard-btn, #gamepad-btn, #touch-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.classList.toggle('active');
            });
        });

        // Rotate functionality
        document.getElementById('rotate-btn').addEventListener('click', function() {
            const screenContent = document.getElementById('screen-content');
            screenContent.style.transform = screenContent.style.transform === 'rotate(90deg)' ? 'rotate(0deg)' : 'rotate(90deg)';
        });

        // Simulate real-time stats updates
        setInterval(() => {
            const latency = Math.floor(Math.random() * 10) + 8;
            const quality = Math.floor(Math.random() * 5) + 95;
            
            document.getElementById('latency-stat').textContent = latency + 'ms';
            document.getElementById('quality-stat').textContent = quality + '%';
        }, 2000);

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F11') {
                e.preventDefault();
                toggleFullscreen();
            } else if (e.key === 'F12') {
                e.preventDefault();
                takeScreenshot();
            } else if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                toggleRecording();
            }
        });

        // Demo app interactions
        document.querySelectorAll('.demo-app').forEach(app => {
            app.addEventListener('click', function() {
                this.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>
</body>
</html>
