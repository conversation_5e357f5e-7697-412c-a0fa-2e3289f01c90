<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone Casting Pro - Universal Device Mirror</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .connection-modes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .mode-card {
            background: linear-gradient(145deg, #f0f0f0, #ffffff);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .mode-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
            border-color: #667eea;
        }

        .mode-card.active {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
            border-color: #667eea;
        }

        .mode-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #667eea;
        }

        .mode-card.active .mode-icon {
            color: white;
        }

        .mode-title {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .mode-description {
            font-size: 0.9rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        .status-panel {
            background: linear-gradient(145deg, #e8f5e8, #f0fff0);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #4CAF50;
        }

        .status-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .status-icon {
            font-size: 1.5rem;
            color: #4CAF50;
            margin-right: 10px;
        }

        .device-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .info-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
        }

        .info-icon {
            margin-right: 10px;
            color: #667eea;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: linear-gradient(145deg, #fff, #f8f9fa);
            border-radius: 12px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }

        .feature-header {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .feature-icon {
            font-size: 1.2rem;
            color: #667eea;
            margin-right: 10px;
        }

        .feature-status {
            margin-left: auto;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-wip {
            background: #fff3cd;
            color: #856404;
        }

        .control-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(145deg, #6c757d, #5a6268);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(108, 117, 125, 0.4);
        }

        .qr-section {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
        }

        .qr-placeholder {
            width: 150px;
            height: 150px;
            background: white;
            border: 2px dashed #667eea;
            border-radius: 10px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #667eea;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .connection-modes {
                grid-template-columns: 1fr;
            }
            
            .control-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-mobile-alt"></i> Phone Casting Pro</h1>
            <p>Universal Device Mirror - Connect via Wi-Fi, Bluetooth, USB or QR Code</p>
        </div>

        <div class="main-panel">
            <!-- Connection Status -->
            <div class="status-panel">
                <div class="status-header">
                    <i class="fas fa-wifi status-icon pulse"></i>
                    <h3>Connection Status</h3>
                </div>
                <div id="connection-status">
                    <p><strong>Status:</strong> <span id="status-text">Ready to Connect</span></p>
                    <div class="device-info" id="device-info" style="display: none;">
                        <div class="info-item">
                            <i class="fas fa-mobile-alt info-icon"></i>
                            <span id="device-name">OnePlus Nord CE</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-signal info-icon"></i>
                            <span id="connection-mode">Wi-Fi Direct</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-video info-icon"></i>
                            <span>Stream Live 🎥</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-mouse info-icon"></i>
                            <span>Input Enabled 🖱️</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Connection Modes -->
            <h2 style="margin-bottom: 20px; text-align: center;">Choose Connection Method</h2>
            <div class="connection-modes">
                <div class="mode-card" data-mode="wifi">
                    <div class="mode-icon">📶</div>
                    <div class="mode-title">Wi-Fi Connection</div>
                    <div class="mode-description">Auto-discover devices on the same network or Wi-Fi Direct</div>
                </div>
                
                <div class="mode-card" data-mode="bluetooth">
                    <div class="mode-icon">🔗</div>
                    <div class="mode-title">Bluetooth</div>
                    <div class="mode-description">Low-latency control and file transfer via Bluetooth pairing</div>
                </div>
                
                <div class="mode-card" data-mode="usb">
                    <div class="mode-icon">🔌</div>
                    <div class="mode-title">USB (ADB)</div>
                    <div class="mode-description">High-performance connection via USB debugging mode</div>
                </div>
                
                <div class="mode-card" data-mode="qr">
                    <div class="mode-icon">🔁</div>
                    <div class="mode-title">QR Scan (USB-Free)</div>
                    <div class="mode-description">Pure wireless operation via QR code pairing</div>
                </div>
            </div>

            <!-- Control Buttons -->
            <div class="control-buttons">
                <button class="btn btn-primary" id="connect-btn">
                    <i class="fas fa-play"></i> Start Connection
                </button>
                <button class="btn btn-secondary" id="scan-btn">
                    <i class="fas fa-search"></i> Scan for Devices
                </button>
                <button class="btn btn-secondary" id="settings-btn">
                    <i class="fas fa-cog"></i> Settings
                </button>
            </div>

            <!-- QR Code Section -->
            <div class="qr-section" id="qr-section" style="display: none;">
                <h3>Scan QR Code on Your Phone</h3>
                <div class="qr-placeholder">
                    <i class="fas fa-qrcode"></i>
                </div>
                <p>Open the Phone Casting app on your device and scan this QR code</p>
            </div>

            <!-- Features Overview -->
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-desktop feature-icon"></i>
                        <span>Screen Mirroring</span>
                        <span class="feature-status status-active">✅ Active</span>
                    </div>
                    <p>Real-time screen mirroring with high quality</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-mouse feature-icon"></i>
                        <span>Input Control</span>
                        <span class="feature-status status-active">✅ Active</span>
                    </div>
                    <p>Mouse and keyboard input with touchpad mode</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-copy feature-icon"></i>
                        <span>Clipboard Sync</span>
                        <span class="feature-status status-wip">🔄 WIP</span>
                    </div>
                    <p>Seamless clipboard and file transfer</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-header">
                        <i class="fas fa-sync feature-icon"></i>
                        <span>Background Sync</span>
                        <span class="feature-status status-active">✅ Active</span>
                    </div>
                    <p>Continuous background synchronization</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Connection mode selection
        document.querySelectorAll('.mode-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove active class from all cards
                document.querySelectorAll('.mode-card').forEach(c => c.classList.remove('active'));
                // Add active class to clicked card
                this.classList.add('active');
                
                const mode = this.dataset.mode;
                updateConnectionMode(mode);
            });
        });

        function updateConnectionMode(mode) {
            const modeNames = {
                'wifi': 'Wi-Fi Connection',
                'bluetooth': 'Bluetooth',
                'usb': 'USB (ADB)',
                'qr': 'QR Scan (USB-Free)'
            };
            
            console.log(`Selected connection mode: ${modeNames[mode]}`);
            
            // Show QR section only for QR mode
            const qrSection = document.getElementById('qr-section');
            if (mode === 'qr') {
                qrSection.style.display = 'block';
            } else {
                qrSection.style.display = 'none';
            }
        }

        // Connect button functionality
        document.getElementById('connect-btn').addEventListener('click', function() {
            const activeMode = document.querySelector('.mode-card.active');
            if (!activeMode) {
                alert('Please select a connection mode first!');
                return;
            }
            
            // Simulate connection process
            const statusText = document.getElementById('status-text');
            const deviceInfo = document.getElementById('device-info');
            const connectBtn = this;
            
            connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting...';
            connectBtn.disabled = true;
            
            setTimeout(() => {
                statusText.textContent = 'Connected Successfully!';
                deviceInfo.style.display = 'grid';
                connectBtn.innerHTML = '<i class="fas fa-stop"></i> Disconnect';
                connectBtn.classList.remove('btn-primary');
                connectBtn.classList.add('btn-secondary');
                connectBtn.disabled = false;
                
                // Update connection mode in device info
                const mode = activeMode.dataset.mode;
                const modeNames = {
                    'wifi': 'Wi-Fi Direct',
                    'bluetooth': 'Bluetooth Classic',
                    'usb': 'USB (ADB)',
                    'qr': 'Wi-Fi Hotspot'
                };
                document.getElementById('connection-mode').textContent = modeNames[mode];
            }, 2000);
        });

        // Scan button functionality
        document.getElementById('scan-btn').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scanning...';
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-search"></i> Scan for Devices';
                alert('Found 2 devices:\n• OnePlus Nord CE (*************)\n• Samsung Galaxy S21 (*************)');
            }, 1500);
        });

        // Settings button functionality
        document.getElementById('settings-btn').addEventListener('click', function() {
            alert('Settings panel would open here with options for:\n• Video quality\n• Audio settings\n• Input sensitivity\n• Connection preferences');
        });

        // Auto-select Wi-Fi mode by default
        document.querySelector('[data-mode="wifi"]').click();
    </script>
</body>
</html>
