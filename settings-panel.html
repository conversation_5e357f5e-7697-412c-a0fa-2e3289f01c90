<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone Casting Pro - Advanced Settings</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .settings-container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .settings-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .settings-header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .settings-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 5px;
            flex-wrap: wrap;
        }

        .tab-btn {
            padding: 12px 20px;
            border: none;
            background: transparent;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 2px;
        }

        .tab-btn.active {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .settings-section {
            background: linear-gradient(145deg, #f8f9fa, #ffffff);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-label {
            flex: 1;
        }

        .setting-label h4 {
            margin-bottom: 5px;
            color: #333;
        }

        .setting-label p {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.4;
        }

        .setting-control {
            min-width: 200px;
            text-align: right;
        }

        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .slider {
            width: 120px;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(145deg, #667eea, #764ba2);
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(145deg, #667eea, #764ba2);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .toggle-switch {
            position: relative;
            width: 60px;
            height: 30px;
            background: #ccc;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle-switch.active {
            background: linear-gradient(145deg, #667eea, #764ba2);
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            background: white;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .toggle-switch.active::after {
            transform: translateX(30px);
        }

        .dropdown {
            padding: 8px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: white;
            font-size: 0.9rem;
            min-width: 150px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dropdown:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .value-display {
            font-weight: bold;
            color: #667eea;
            min-width: 40px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(145deg, #6c757d, #5a6268);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(145deg, #dc3545, #c82333);
            color: white;
        }

        .connection-test {
            background: linear-gradient(145deg, #e8f5e8, #f0fff0);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }

        .test-result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 8px;
            font-weight: bold;
        }

        .test-success {
            background: #d4edda;
            color: #155724;
        }

        .test-warning {
            background: #fff3cd;
            color: #856404;
        }

        @media (max-width: 768px) {
            .settings-container {
                padding: 20px;
                margin: 10px;
            }
            
            .settings-tabs {
                flex-direction: column;
            }
            
            .tab-btn {
                width: 100%;
                justify-content: center;
            }
            
            .setting-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .setting-control {
                width: 100%;
                text-align: left;
            }
        }
    </style>
</head>
<body>
    <div class="settings-container">
        <div class="settings-header">
            <h1><i class="fas fa-cog"></i> Advanced Settings</h1>
            <p>Configure your Phone Casting Pro experience</p>
        </div>

        <div class="settings-tabs">
            <button class="tab-btn active" data-tab="video">
                <i class="fas fa-video"></i> Video
            </button>
            <button class="tab-btn" data-tab="audio">
                <i class="fas fa-volume-up"></i> Audio
            </button>
            <button class="tab-btn" data-tab="input">
                <i class="fas fa-mouse"></i> Input
            </button>
            <button class="tab-btn" data-tab="connection">
                <i class="fas fa-wifi"></i> Connection
            </button>
            <button class="tab-btn" data-tab="advanced">
                <i class="fas fa-tools"></i> Advanced
            </button>
        </div>

        <!-- Video Settings -->
        <div class="tab-content active" id="video">
            <div class="settings-section">
                <div class="section-title">
                    <i class="fas fa-display"></i>
                    Video Quality Settings
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Resolution</h4>
                        <p>Set the streaming resolution for optimal performance</p>
                    </div>
                    <div class="setting-control">
                        <select class="dropdown" id="resolution">
                            <option value="1080p">1920x1080 (1080p)</option>
                            <option value="720p" selected>1280x720 (720p)</option>
                            <option value="480p">854x480 (480p)</option>
                            <option value="auto">Auto (Adaptive)</option>
                        </select>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Frame Rate</h4>
                        <p>Higher frame rates provide smoother video but use more bandwidth</p>
                    </div>
                    <div class="setting-control">
                        <div class="slider-container">
                            <span>15fps</span>
                            <input type="range" class="slider" id="framerate" min="15" max="60" value="30">
                            <span>60fps</span>
                            <span class="value-display" id="framerate-value">30fps</span>
                        </div>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Bitrate</h4>
                        <p>Control video quality vs bandwidth usage</p>
                    </div>
                    <div class="setting-control">
                        <div class="slider-container">
                            <span>1Mbps</span>
                            <input type="range" class="slider" id="bitrate" min="1" max="20" value="8">
                            <span>20Mbps</span>
                            <span class="value-display" id="bitrate-value">8Mbps</span>
                        </div>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Hardware Acceleration</h4>
                        <p>Use GPU acceleration for better performance</p>
                    </div>
                    <div class="setting-control">
                        <div class="toggle-switch active" id="hw-accel"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Audio Settings -->
        <div class="tab-content" id="audio">
            <div class="settings-section">
                <div class="section-title">
                    <i class="fas fa-headphones"></i>
                    Audio Configuration
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Audio Quality</h4>
                        <p>Select audio streaming quality</p>
                    </div>
                    <div class="setting-control">
                        <select class="dropdown" id="audio-quality">
                            <option value="high">High (320kbps)</option>
                            <option value="medium" selected>Medium (192kbps)</option>
                            <option value="low">Low (128kbps)</option>
                        </select>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Audio Latency</h4>
                        <p>Adjust audio delay compensation</p>
                    </div>
                    <div class="setting-control">
                        <div class="slider-container">
                            <span>0ms</span>
                            <input type="range" class="slider" id="audio-latency" min="0" max="500" value="100">
                            <span>500ms</span>
                            <span class="value-display" id="latency-value">100ms</span>
                        </div>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Microphone Passthrough</h4>
                        <p>Enable microphone from laptop to phone</p>
                    </div>
                    <div class="setting-control">
                        <div class="toggle-switch" id="mic-passthrough"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Input Settings -->
        <div class="tab-content" id="input">
            <div class="settings-section">
                <div class="section-title">
                    <i class="fas fa-hand-pointer"></i>
                    Input Control Settings
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Mouse Sensitivity</h4>
                        <p>Adjust cursor movement sensitivity</p>
                    </div>
                    <div class="setting-control">
                        <div class="slider-container">
                            <span>Low</span>
                            <input type="range" class="slider" id="mouse-sensitivity" min="1" max="10" value="5">
                            <span>High</span>
                            <span class="value-display" id="sensitivity-value">5</span>
                        </div>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Touchpad Mode</h4>
                        <p>Enable laptop touchpad gestures</p>
                    </div>
                    <div class="setting-control">
                        <div class="toggle-switch active" id="touchpad-mode"></div>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Keyboard Layout</h4>
                        <p>Select keyboard layout for input</p>
                    </div>
                    <div class="setting-control">
                        <select class="dropdown" id="keyboard-layout">
                            <option value="qwerty" selected>QWERTY</option>
                            <option value="azerty">AZERTY</option>
                            <option value="qwertz">QWERTZ</option>
                            <option value="dvorak">Dvorak</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Connection Settings -->
        <div class="tab-content" id="connection">
            <div class="settings-section">
                <div class="section-title">
                    <i class="fas fa-network-wired"></i>
                    Connection Preferences
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Auto-Connect</h4>
                        <p>Automatically connect to last known device</p>
                    </div>
                    <div class="setting-control">
                        <div class="toggle-switch active" id="auto-connect"></div>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Connection Timeout</h4>
                        <p>Time to wait before connection attempt fails</p>
                    </div>
                    <div class="setting-control">
                        <div class="slider-container">
                            <span>5s</span>
                            <input type="range" class="slider" id="timeout" min="5" max="60" value="15">
                            <span>60s</span>
                            <span class="value-display" id="timeout-value">15s</span>
                        </div>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Fallback Mode</h4>
                        <p>Automatically try alternative connection methods</p>
                    </div>
                    <div class="setting-control">
                        <div class="toggle-switch active" id="fallback-mode"></div>
                    </div>
                </div>
            </div>

            <div class="connection-test">
                <h3><i class="fas fa-wifi"></i> Connection Test</h3>
                <p>Test your current connection quality and performance</p>
                <button class="btn btn-primary" id="test-connection">
                    <i class="fas fa-play"></i> Run Test
                </button>
                <div class="test-result" id="test-result" style="display: none;"></div>
            </div>
        </div>

        <!-- Advanced Settings -->
        <div class="tab-content" id="advanced">
            <div class="settings-section">
                <div class="section-title">
                    <i class="fas fa-code"></i>
                    Advanced Configuration
                </div>
                
                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Debug Mode</h4>
                        <p>Enable detailed logging and debug information</p>
                    </div>
                    <div class="setting-control">
                        <div class="toggle-switch" id="debug-mode"></div>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Performance Monitoring</h4>
                        <p>Show real-time performance metrics</p>
                    </div>
                    <div class="setting-control">
                        <div class="toggle-switch" id="perf-monitor"></div>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <h4>Custom Port</h4>
                        <p>Override default connection port</p>
                    </div>
                    <div class="setting-control">
                        <input type="number" class="dropdown" id="custom-port" value="8080" min="1024" max="65535">
                    </div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button class="btn btn-primary" id="save-settings">
                <i class="fas fa-save"></i> Save Settings
            </button>
            <button class="btn btn-secondary" id="reset-defaults">
                <i class="fas fa-undo"></i> Reset to Defaults
            </button>
            <button class="btn btn-danger" id="clear-data">
                <i class="fas fa-trash"></i> Clear All Data
            </button>
        </div>
    </div>

    <script>
        // Tab switching functionality
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all tabs and contents
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });

        // Toggle switches
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', function() {
                this.classList.toggle('active');
            });
        });

        // Slider value updates
        const sliders = [
            { id: 'framerate', display: 'framerate-value', suffix: 'fps' },
            { id: 'bitrate', display: 'bitrate-value', suffix: 'Mbps' },
            { id: 'audio-latency', display: 'latency-value', suffix: 'ms' },
            { id: 'mouse-sensitivity', display: 'sensitivity-value', suffix: '' },
            { id: 'timeout', display: 'timeout-value', suffix: 's' }
        ];

        sliders.forEach(slider => {
            const element = document.getElementById(slider.id);
            const display = document.getElementById(slider.display);
            
            element.addEventListener('input', function() {
                display.textContent = this.value + slider.suffix;
            });
        });

        // Connection test
        document.getElementById('test-connection').addEventListener('click', function() {
            const btn = this;
            const result = document.getElementById('test-result');
            
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
            btn.disabled = true;
            result.style.display = 'none';
            
            setTimeout(() => {
                const tests = [
                    { name: 'Latency', value: '12ms', status: 'good' },
                    { name: 'Bandwidth', value: '45.2 Mbps', status: 'good' },
                    { name: 'Packet Loss', value: '0.1%', status: 'good' },
                    { name: 'Jitter', value: '2ms', status: 'warning' }
                ];
                
                let resultHTML = '<h4>Connection Test Results:</h4>';
                tests.forEach(test => {
                    const icon = test.status === 'good' ? '✅' : '⚠️';
                    resultHTML += `<div>${icon} ${test.name}: ${test.value}</div>`;
                });
                
                result.innerHTML = resultHTML;
                result.className = 'test-result test-success';
                result.style.display = 'block';
                
                btn.innerHTML = '<i class="fas fa-play"></i> Run Test';
                btn.disabled = false;
            }, 3000);
        });

        // Save settings
        document.getElementById('save-settings').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-check"></i> Saved!';
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-save"></i> Save Settings';
                }, 2000);
            }, 1000);
        });

        // Reset defaults
        document.getElementById('reset-defaults').addEventListener('click', function() {
            if (confirm('Are you sure you want to reset all settings to defaults?')) {
                location.reload();
            }
        });

        // Clear data
        document.getElementById('clear-data').addEventListener('click', function() {
            if (confirm('This will clear all saved data including device pairings. Continue?')) {
                alert('All data cleared successfully!');
            }
        });
    </script>
</body>
</html>
