<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone Casting Pro - Complete Interface Suite</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #667eea;
            font-size: 3rem;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .header p {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(145deg, #4CAF50, #45a049);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: white;
            animation: pulse 2s infinite;
        }

        .components-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .component-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #667eea;
            cursor: pointer;
        }

        .component-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            border-left-color: #4CAF50;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }

        .card-icon {
            font-size: 2rem;
            color: #667eea;
            width: 50px;
            height: 50px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
        }

        .card-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .card-features {
            list-style: none;
            margin-bottom: 20px;
        }

        .card-features li {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 0.9rem;
            color: #555;
        }

        .card-features i {
            color: #4CAF50;
            font-size: 0.8rem;
        }

        .launch-btn {
            width: 100%;
            padding: 12px 20px;
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .launch-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .stats-section {
            background: linear-gradient(145deg, #f8f9fa, #ffffff);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .stats-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }

        .stat-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .footer {
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
            color: #666;
        }

        .tech-stack {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .tech-item {
            display: flex;
            align-items: center;
            gap: 5px;
            background: rgba(102, 126, 234, 0.1);
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            color: #667eea;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 20px;
                margin: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .components-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1>
                <i class="fas fa-mobile-alt"></i>
                Phone Casting Pro
            </h1>
            <p>Complete Interface Suite - Seamlessly connect and control your devices</p>
            <div class="status-badge">
                <div class="status-dot"></div>
                All Components Ready
            </div>
        </div>

        <div class="stats-section">
            <div class="stats-title">Interface Components Overview</div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">6</div>
                    <div class="stat-label">Components</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">4</div>
                    <div class="stat-label">Connection Modes</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">100%</div>
                    <div class="stat-label">Responsive</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">15+</div>
                    <div class="stat-label">Features</div>
                </div>
            </div>
        </div>

        <div class="components-grid">
            <div class="component-card" onclick="window.open('phone-casting-interface.html', '_blank')">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="card-title">Main Interface</div>
                </div>
                <div class="card-description">
                    Primary connection interface with multiple casting modes and device pairing capabilities.
                </div>
                <ul class="card-features">
                    <li><i class="fas fa-check"></i> Wi-Fi, Bluetooth, USB, QR connection modes</li>
                    <li><i class="fas fa-check"></i> Real-time device status monitoring</li>
                    <li><i class="fas fa-check"></i> Interactive connection setup</li>
                    <li><i class="fas fa-check"></i> Feature overview dashboard</li>
                </ul>
                <button class="launch-btn">
                    <i class="fas fa-rocket"></i> Launch Main Interface
                </button>
            </div>

            <div class="component-card" onclick="window.open('settings-panel.html', '_blank')">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="card-title">Advanced Settings</div>
                </div>
                <div class="card-description">
                    Comprehensive settings panel for video quality, audio, input controls, and connection preferences.
                </div>
                <ul class="card-features">
                    <li><i class="fas fa-check"></i> Video quality & performance tuning</li>
                    <li><i class="fas fa-check"></i> Audio settings & latency control</li>
                    <li><i class="fas fa-check"></i> Input sensitivity configuration</li>
                    <li><i class="fas fa-check"></i> Connection testing tools</li>
                </ul>
                <button class="launch-btn">
                    <i class="fas fa-sliders-h"></i> Open Settings Panel
                </button>
            </div>

            <div class="component-card" onclick="window.open('device-discovery.html', '_blank')">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="card-title">Device Discovery</div>
                </div>
                <div class="card-description">
                    Discover and manage available devices with filtering options and connection details.
                </div>
                <ul class="card-features">
                    <li><i class="fas fa-check"></i> Auto-discovery of nearby devices</li>
                    <li><i class="fas fa-check"></i> Signal strength indicators</li>
                    <li><i class="fas fa-check"></i> Device filtering & sorting</li>
                    <li><i class="fas fa-check"></i> Connection mode compatibility</li>
                </ul>
                <button class="launch-btn">
                    <i class="fas fa-radar"></i> Launch Device Discovery
                </button>
            </div>

            <div class="component-card" onclick="window.open('screen-mirroring-viewer.html', '_blank')">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <div class="card-title">Screen Mirroring Viewer</div>
                </div>
                <div class="card-description">
                    Full-screen mirroring interface with recording capabilities and performance monitoring.
                </div>
                <ul class="card-features">
                    <li><i class="fas fa-check"></i> Full-screen mirroring display</li>
                    <li><i class="fas fa-check"></i> Screen recording & screenshots</li>
                    <li><i class="fas fa-check"></i> Real-time performance stats</li>
                    <li><i class="fas fa-check"></i> Quality adjustment controls</li>
                </ul>
                <button class="launch-btn">
                    <i class="fas fa-tv"></i> Open Screen Viewer
                </button>
            </div>

            <div class="component-card" onclick="window.open('file-transfer-interface.html', '_blank')">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="card-title">File Transfer & Clipboard</div>
                </div>
                <div class="card-description">
                    Drag-and-drop file transfer with real-time progress tracking and clipboard synchronization.
                </div>
                <ul class="card-features">
                    <li><i class="fas fa-check"></i> Drag & drop file transfers</li>
                    <li><i class="fas fa-check"></i> Real-time progress tracking</li>
                    <li><i class="fas fa-check"></i> Clipboard synchronization</li>
                    <li><i class="fas fa-check"></i> Transfer history & statistics</li>
                </ul>
                <button class="launch-btn">
                    <i class="fas fa-file-export"></i> Launch File Transfer
                </button>
            </div>

            <div class="component-card" onclick="window.open('mobile-companion-app.html', '_blank')">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="card-title">Mobile Companion App</div>
                </div>
                <div class="card-description">
                    Touch-optimized mobile interface for Android devices with native app experience.
                </div>
                <ul class="card-features">
                    <li><i class="fas fa-check"></i> Mobile-optimized touch interface</li>
                    <li><i class="fas fa-check"></i> Permission management system</li>
                    <li><i class="fas fa-check"></i> QR code quick connect</li>
                    <li><i class="fas fa-check"></i> Real-time connection status</li>
                </ul>
                <button class="launch-btn">
                    <i class="fas fa-mobile"></i> Open Mobile App
                </button>
            </div>
        </div>

        <div class="footer">
            <p><strong>Phone Casting Pro</strong> - Complete Interface Suite</p>
            <p>Built with modern web technologies for seamless device connectivity</p>
            <div class="tech-stack">
                <div class="tech-item">
                    <i class="fab fa-html5"></i> HTML5
                </div>
                <div class="tech-item">
                    <i class="fab fa-css3-alt"></i> CSS3
                </div>
                <div class="tech-item">
                    <i class="fab fa-js-square"></i> JavaScript
                </div>
                <div class="tech-item">
                    <i class="fas fa-mobile-alt"></i> Responsive
                </div>
                <div class="tech-item">
                    <i class="fas fa-palette"></i> Modern UI
                </div>
            </div>
        </div>
    </div>
</body>
</html>
