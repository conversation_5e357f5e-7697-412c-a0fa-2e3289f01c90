<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone Casting Pro - File Transfer & Clipboard Sync</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .transfer-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .transfer-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .transfer-header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .transfer-section {
            background: linear-gradient(145deg, #f8f9fa, #ffffff);
            border-radius: 15px;
            padding: 25px;
            border-left: 4px solid #667eea;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .drop-zone {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px 20px;
            text-align: center;
            background: rgba(102, 126, 234, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .drop-zone.dragover {
            border-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
            transform: scale(1.02);
        }

        .drop-zone i {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .drop-zone h3 {
            margin-bottom: 10px;
            color: #333;
        }

        .drop-zone p {
            color: #666;
            margin-bottom: 15px;
        }

        .file-input {
            display: none;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(145deg, #6c757d, #5a6268);
            color: white;
        }

        .file-list {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .file-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: white;
            border-radius: 8px;
            margin-bottom: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .file-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .file-icon {
            font-size: 1.5rem;
            margin-right: 12px;
            width: 30px;
            text-align: center;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 500;
            margin-bottom: 2px;
        }

        .file-size {
            font-size: 0.8rem;
            color: #666;
        }

        .file-progress {
            width: 100%;
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            margin-top: 5px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .file-status {
            margin-left: 10px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-uploading {
            color: #2196F3;
        }

        .status-completed {
            color: #4CAF50;
        }

        .status-error {
            color: #f44336;
        }

        .clipboard-section {
            background: linear-gradient(145deg, #e8f5e8, #f0fff0);
            border-left-color: #4CAF50;
        }

        .clipboard-content {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
            min-height: 100px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.4;
            resize: vertical;
            width: 100%;
        }

        .clipboard-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .sync-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 15px;
            background: rgba(76, 175, 80, 0.1);
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .sync-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        .transfer-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .recent-transfers {
            margin-top: 30px;
        }

        .transfer-history {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .history-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-icon {
            font-size: 1.2rem;
            margin-right: 12px;
            width: 25px;
            text-align: center;
        }

        .history-info {
            flex: 1;
        }

        .history-name {
            font-weight: 500;
            margin-bottom: 2px;
        }

        .history-time {
            font-size: 0.8rem;
            color: #666;
        }

        .history-direction {
            font-size: 0.8rem;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: bold;
        }

        .direction-sent {
            background: #e3f2fd;
            color: #1976d2;
        }

        .direction-received {
            background: #e8f5e8;
            color: #388e3c;
        }

        @media (max-width: 768px) {
            .transfer-container {
                padding: 20px;
                margin: 10px;
            }
            
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .transfer-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="transfer-container">
        <div class="transfer-header">
            <h1><i class="fas fa-exchange-alt"></i> File Transfer & Clipboard Sync</h1>
            <p>Seamlessly transfer files and sync clipboard between devices</p>
        </div>

        <div class="main-content">
            <!-- File Transfer Section -->
            <div class="transfer-section">
                <div class="section-title">
                    <i class="fas fa-file-upload"></i>
                    File Transfer
                </div>

                <div class="drop-zone" id="drop-zone">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <h3>Drop files here to transfer</h3>
                    <p>Or click to browse and select files</p>
                    <button class="btn btn-primary" onclick="document.getElementById('file-input').click()">
                        <i class="fas fa-folder-open"></i> Browse Files
                    </button>
                </div>

                <input type="file" id="file-input" class="file-input" multiple>

                <div class="file-list" id="file-list">
                    <!-- Files will be added here dynamically -->
                </div>
            </div>

            <!-- Clipboard Sync Section -->
            <div class="transfer-section clipboard-section">
                <div class="section-title">
                    <i class="fas fa-clipboard"></i>
                    Clipboard Sync
                </div>

                <div class="sync-status">
                    <div class="sync-indicator"></div>
                    <span>Clipboard sync active - OnePlus Nord CE</span>
                </div>

                <textarea class="clipboard-content" id="clipboard-content" placeholder="Clipboard content will appear here automatically...">Hello from your phone! This text was copied on your mobile device and automatically synced to your laptop.</textarea>

                <div class="clipboard-actions">
                    <button class="btn btn-primary" id="copy-btn">
                        <i class="fas fa-copy"></i> Copy to Clipboard
                    </button>
                    <button class="btn btn-secondary" id="paste-btn">
                        <i class="fas fa-paste"></i> Paste from Clipboard
                    </button>
                    <button class="btn btn-secondary" id="clear-btn">
                        <i class="fas fa-trash"></i> Clear
                    </button>
                </div>
            </div>
        </div>

        <!-- Transfer Statistics -->
        <div class="transfer-stats">
            <div class="stat-card">
                <div class="stat-value" id="files-transferred">24</div>
                <div class="stat-label">Files Transferred</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="data-transferred">1.2GB</div>
                <div class="stat-label">Data Transferred</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="clipboard-syncs">156</div>
                <div class="stat-label">Clipboard Syncs</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="transfer-speed">45MB/s</div>
                <div class="stat-label">Average Speed</div>
            </div>
        </div>

        <!-- Recent Transfers -->
        <div class="recent-transfers">
            <div class="section-title">
                <i class="fas fa-history"></i>
                Recent Transfers
            </div>
            <div class="transfer-history">
                <div class="history-item">
                    <i class="fas fa-image history-icon" style="color: #FF9800;"></i>
                    <div class="history-info">
                        <div class="history-name">vacation_photos.zip</div>
                        <div class="history-time">2 minutes ago</div>
                    </div>
                    <div class="history-direction direction-received">Received</div>
                </div>
                <div class="history-item">
                    <i class="fas fa-file-pdf history-icon" style="color: #f44336;"></i>
                    <div class="history-info">
                        <div class="history-name">presentation.pdf</div>
                        <div class="history-time">5 minutes ago</div>
                    </div>
                    <div class="history-direction direction-sent">Sent</div>
                </div>
                <div class="history-item">
                    <i class="fas fa-music history-icon" style="color: #9C27B0;"></i>
                    <div class="history-info">
                        <div class="history-name">favorite_song.mp3</div>
                        <div class="history-time">12 minutes ago</div>
                    </div>
                    <div class="history-direction direction-received">Received</div>
                </div>
                <div class="history-item">
                    <i class="fas fa-file-word history-icon" style="color: #2196F3;"></i>
                    <div class="history-info">
                        <div class="history-name">document.docx</div>
                        <div class="history-time">1 hour ago</div>
                    </div>
                    <div class="history-direction direction-sent">Sent</div>
                </div>
                <div class="history-item">
                    <i class="fas fa-clipboard history-icon" style="color: #4CAF50;"></i>
                    <div class="history-info">
                        <div class="history-name">Clipboard: "Meeting at 3 PM"</div>
                        <div class="history-time">2 hours ago</div>
                    </div>
                    <div class="history-direction direction-received">Synced</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let fileQueue = [];
        let transferredFiles = 24;
        let totalDataTransferred = 1.2; // GB
        let clipboardSyncs = 156;

        // File type icons mapping
        const fileIcons = {
            'pdf': { icon: 'fas fa-file-pdf', color: '#f44336' },
            'doc': { icon: 'fas fa-file-word', color: '#2196F3' },
            'docx': { icon: 'fas fa-file-word', color: '#2196F3' },
            'xls': { icon: 'fas fa-file-excel', color: '#4CAF50' },
            'xlsx': { icon: 'fas fa-file-excel', color: '#4CAF50' },
            'ppt': { icon: 'fas fa-file-powerpoint', color: '#FF9800' },
            'pptx': { icon: 'fas fa-file-powerpoint', color: '#FF9800' },
            'jpg': { icon: 'fas fa-image', color: '#FF9800' },
            'jpeg': { icon: 'fas fa-image', color: '#FF9800' },
            'png': { icon: 'fas fa-image', color: '#FF9800' },
            'gif': { icon: 'fas fa-image', color: '#FF9800' },
            'mp4': { icon: 'fas fa-video', color: '#E91E63' },
            'avi': { icon: 'fas fa-video', color: '#E91E63' },
            'mp3': { icon: 'fas fa-music', color: '#9C27B0' },
            'wav': { icon: 'fas fa-music', color: '#9C27B0' },
            'zip': { icon: 'fas fa-file-archive', color: '#607D8B' },
            'rar': { icon: 'fas fa-file-archive', color: '#607D8B' },
            'txt': { icon: 'fas fa-file-alt', color: '#666' },
            'default': { icon: 'fas fa-file', color: '#666' }
        };

        function getFileIcon(filename) {
            const extension = filename.split('.').pop().toLowerCase();
            return fileIcons[extension] || fileIcons.default;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function addFileToQueue(file) {
            const fileId = Date.now() + Math.random();
            const fileObj = {
                id: fileId,
                file: file,
                name: file.name,
                size: file.size,
                progress: 0,
                status: 'queued'
            };

            fileQueue.push(fileObj);
            renderFileList();
            startFileTransfer(fileObj);
        }

        function renderFileList() {
            const fileList = document.getElementById('file-list');

            if (fileQueue.length === 0) {
                fileList.innerHTML = '<p style="text-align: center; color: #666; padding: 20px;">No files in queue</p>';
                return;
            }

            fileList.innerHTML = fileQueue.map(file => {
                const iconInfo = getFileIcon(file.name);
                let statusClass = '';
                let statusText = '';

                switch (file.status) {
                    case 'queued':
                        statusClass = 'status-uploading';
                        statusText = 'Queued';
                        break;
                    case 'uploading':
                        statusClass = 'status-uploading';
                        statusText = `${file.progress}%`;
                        break;
                    case 'completed':
                        statusClass = 'status-completed';
                        statusText = 'Completed';
                        break;
                    case 'error':
                        statusClass = 'status-error';
                        statusText = 'Error';
                        break;
                }

                return `
                    <div class="file-item">
                        <i class="${iconInfo.icon} file-icon" style="color: ${iconInfo.color};"></i>
                        <div class="file-info">
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${formatFileSize(file.size)}</div>
                            ${file.status === 'uploading' ? `
                                <div class="file-progress">
                                    <div class="progress-bar" style="width: ${file.progress}%"></div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="file-status ${statusClass}">${statusText}</div>
                    </div>
                `;
            }).join('');
        }

        function startFileTransfer(fileObj) {
            fileObj.status = 'uploading';
            renderFileList();

            // Simulate file transfer progress
            const transferInterval = setInterval(() => {
                fileObj.progress += Math.random() * 15 + 5;

                if (fileObj.progress >= 100) {
                    fileObj.progress = 100;
                    fileObj.status = 'completed';
                    clearInterval(transferInterval);

                    // Update statistics
                    transferredFiles++;
                    totalDataTransferred += fileObj.size / (1024 * 1024 * 1024);
                    updateStats();

                    // Show completion notification
                    setTimeout(() => {
                        alert(`File "${fileObj.name}" transferred successfully!`);
                    }, 500);
                }

                renderFileList();
            }, 200);
        }

        function updateStats() {
            document.getElementById('files-transferred').textContent = transferredFiles;
            document.getElementById('data-transferred').textContent = totalDataTransferred.toFixed(1) + 'GB';
            document.getElementById('clipboard-syncs').textContent = clipboardSyncs;
        }

        // Drag and drop functionality
        const dropZone = document.getElementById('drop-zone');
        const fileInput = document.getElementById('file-input');

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files);
            files.forEach(file => addFileToQueue(file));
        });

        dropZone.addEventListener('click', () => {
            fileInput.click();
        });

        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            files.forEach(file => addFileToQueue(file));
            e.target.value = ''; // Reset input
        });

        // Clipboard functionality
        const clipboardContent = document.getElementById('clipboard-content');
        const copyBtn = document.getElementById('copy-btn');
        const pasteBtn = document.getElementById('paste-btn');
        const clearBtn = document.getElementById('clear-btn');

        copyBtn.addEventListener('click', async () => {
            try {
                await navigator.clipboard.writeText(clipboardContent.value);
                copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                setTimeout(() => {
                    copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy to Clipboard';
                }, 2000);

                clipboardSyncs++;
                updateStats();
            } catch (err) {
                alert('Failed to copy to clipboard');
            }
        });

        pasteBtn.addEventListener('click', async () => {
            try {
                const text = await navigator.clipboard.readText();
                clipboardContent.value = text;

                clipboardSyncs++;
                updateStats();
            } catch (err) {
                alert('Failed to read from clipboard');
            }
        });

        clearBtn.addEventListener('click', () => {
            clipboardContent.value = '';
        });

        // Simulate clipboard sync from phone
        setInterval(() => {
            if (Math.random() < 0.1) { // 10% chance every 5 seconds
                const sampleTexts = [
                    "https://example.com/shared-link",
                    "Meeting moved to 4 PM tomorrow",
                    "Don't forget to buy groceries",
                    "New phone number: ****** 567 8900",
                    "Check out this article: https://news.example.com"
                ];

                const randomText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
                clipboardContent.value = randomText;

                // Show sync notification
                const syncStatus = document.querySelector('.sync-status span');
                const originalText = syncStatus.textContent;
                syncStatus.textContent = 'New clipboard content synced from phone!';
                syncStatus.style.color = '#4CAF50';

                setTimeout(() => {
                    syncStatus.textContent = originalText;
                    syncStatus.style.color = '';
                }, 3000);

                clipboardSyncs++;
                updateStats();
            }
        }, 5000);

        // Initialize
        renderFileList();
    </script>
</body>
</html>
