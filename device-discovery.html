<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone Casting Pro - Device Discovery</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            padding: 20px;
        }

        .discovery-container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .discovery-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .discovery-header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .scan-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(145deg, #6c757d, #5a6268);
            color: white;
        }

        .scan-status {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(145deg, #e3f2fd, #f0f8ff);
            border-radius: 10px;
            border-left: 4px solid #2196F3;
        }

        .scanning {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        .devices-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .device-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .device-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .device-card.connected {
            border-color: #4CAF50;
            background: linear-gradient(145deg, #e8f5e8, #f0fff0);
        }

        .device-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .device-icon {
            font-size: 2.5rem;
            margin-right: 15px;
            color: #667eea;
        }

        .device-info h3 {
            font-size: 1.2rem;
            margin-bottom: 5px;
            color: #333;
        }

        .device-info p {
            color: #666;
            font-size: 0.9rem;
        }

        .device-status {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-available {
            background: #d4edda;
            color: #155724;
        }

        .status-connected {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-busy {
            background: #fff3cd;
            color: #856404;
        }

        .device-details {
            margin: 15px 0;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 500;
            color: #555;
        }

        .detail-value {
            color: #333;
            font-weight: bold;
        }

        .signal-strength {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .signal-bars {
            display: flex;
            gap: 2px;
        }

        .signal-bar {
            width: 4px;
            height: 12px;
            background: #ddd;
            border-radius: 2px;
        }

        .signal-bar.active {
            background: #4CAF50;
        }

        .signal-bar.medium {
            background: #FF9800;
        }

        .signal-bar.weak {
            background: #F44336;
        }

        .connection-modes {
            display: flex;
            gap: 8px;
            margin: 10px 0;
            flex-wrap: wrap;
        }

        .mode-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
        }

        .mode-wifi {
            background: #e3f2fd;
            color: #1976d2;
        }

        .mode-bluetooth {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .mode-usb {
            background: #e8f5e8;
            color: #388e3c;
        }

        .device-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 0.9rem;
            flex: 1;
        }

        .btn-connect {
            background: linear-gradient(145deg, #4CAF50, #45a049);
            color: white;
        }

        .btn-info {
            background: linear-gradient(145deg, #2196F3, #1976d2);
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }

        .filter-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 2px solid #e9ecef;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .filter-btn.active {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
            border-color: #667eea;
        }

        .device-count {
            margin-left: auto;
            color: #666;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .discovery-container {
                padding: 20px;
                margin: 10px;
            }
            
            .devices-grid {
                grid-template-columns: 1fr;
            }
            
            .scan-controls {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="discovery-container">
        <div class="discovery-header">
            <h1><i class="fas fa-search"></i> Device Discovery</h1>
            <p>Find and connect to available devices</p>
        </div>

        <div class="scan-controls">
            <button class="btn btn-primary" id="scan-btn">
                <i class="fas fa-search"></i> Scan for Devices
            </button>
            <button class="btn btn-secondary" id="refresh-btn">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <button class="btn btn-secondary" id="manual-connect-btn">
                <i class="fas fa-plus"></i> Manual Connect
            </button>
        </div>

        <div class="scan-status" id="scan-status" style="display: none;">
            <i class="fas fa-spinner fa-spin"></i>
            <span id="scan-text">Scanning for devices...</span>
        </div>

        <div class="filter-controls">
            <button class="filter-btn active" data-filter="all">All Devices</button>
            <button class="filter-btn" data-filter="wifi">📶 Wi-Fi</button>
            <button class="filter-btn" data-filter="bluetooth">🔗 Bluetooth</button>
            <button class="filter-btn" data-filter="usb">🔌 USB</button>
            <button class="filter-btn" data-filter="available">Available Only</button>
            <div class="device-count" id="device-count">0 devices found</div>
        </div>

        <div class="devices-grid" id="devices-grid">
            <!-- Devices will be populated here -->
        </div>

        <div class="empty-state" id="empty-state">
            <i class="fas fa-mobile-alt"></i>
            <h3>No Devices Found</h3>
            <p>Click "Scan for Devices" to search for available devices on your network</p>
        </div>
    </div>

    <script>
        const devices = [
            {
                id: 1,
                name: "OnePlus Nord CE",
                model: "OnePlus Nord CE 2 Lite",
                ip: "*************",
                status: "available",
                signalStrength: 4,
                modes: ["wifi", "bluetooth", "usb"],
                battery: 85,
                lastSeen: "Just now"
            },
            {
                id: 2,
                name: "Samsung Galaxy S21",
                model: "Samsung Galaxy S21 Ultra",
                ip: "*************",
                status: "connected",
                signalStrength: 5,
                modes: ["wifi", "bluetooth"],
                battery: 92,
                lastSeen: "Connected"
            },
            {
                id: 3,
                name: "iPhone 13 Pro",
                model: "Apple iPhone 13 Pro",
                ip: "*************",
                status: "busy",
                signalStrength: 3,
                modes: ["wifi"],
                battery: 67,
                lastSeen: "2 minutes ago"
            },
            {
                id: 4,
                name: "Pixel 7",
                model: "Google Pixel 7",
                ip: "*************",
                status: "available",
                signalStrength: 2,
                modes: ["wifi", "usb"],
                battery: 45,
                lastSeen: "5 minutes ago"
            }
        ];

        let currentFilter = 'all';
        let isScanning = false;

        function getDeviceIcon(model) {
            if (model.includes('iPhone')) return 'fab fa-apple';
            if (model.includes('Samsung')) return 'fas fa-mobile-alt';
            if (model.includes('OnePlus')) return 'fas fa-mobile-alt';
            if (model.includes('Pixel')) return 'fab fa-google';
            return 'fas fa-mobile-alt';
        }

        function getSignalBars(strength) {
            let bars = '';
            for (let i = 1; i <= 5; i++) {
                let className = 'signal-bar';
                if (i <= strength) {
                    if (strength >= 4) className += ' active';
                    else if (strength >= 2) className += ' medium';
                    else className += ' weak';
                }
                bars += `<div class="${className}"></div>`;
            }
            return bars;
        }

        function getModesBadges(modes) {
            const modeIcons = {
                wifi: '📶 Wi-Fi',
                bluetooth: '🔗 Bluetooth',
                usb: '🔌 USB'
            };
            
            return modes.map(mode => 
                `<span class="mode-badge mode-${mode}">${modeIcons[mode]}</span>`
            ).join('');
        }

        function renderDevices() {
            const grid = document.getElementById('devices-grid');
            const emptyState = document.getElementById('empty-state');
            const deviceCount = document.getElementById('device-count');
            
            let filteredDevices = devices;
            
            if (currentFilter !== 'all') {
                if (currentFilter === 'available') {
                    filteredDevices = devices.filter(d => d.status === 'available');
                } else {
                    filteredDevices = devices.filter(d => d.modes.includes(currentFilter));
                }
            }
            
            deviceCount.textContent = `${filteredDevices.length} device${filteredDevices.length !== 1 ? 's' : ''} found`;
            
            if (filteredDevices.length === 0) {
                grid.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }
            
            grid.style.display = 'grid';
            emptyState.style.display = 'none';
            
            grid.innerHTML = filteredDevices.map(device => `
                <div class="device-card ${device.status === 'connected' ? 'connected' : ''}" data-device-id="${device.id}">
                    <div class="device-status status-${device.status}">
                        ${device.status === 'available' ? 'Available' : 
                          device.status === 'connected' ? 'Connected' : 'Busy'}
                    </div>
                    
                    <div class="device-header">
                        <i class="${getDeviceIcon(device.model)} device-icon"></i>
                        <div class="device-info">
                            <h3>${device.name}</h3>
                            <p>${device.model}</p>
                        </div>
                    </div>
                    
                    <div class="connection-modes">
                        ${getModesBadges(device.modes)}
                    </div>
                    
                    <div class="device-details">
                        <div class="detail-row">
                            <span class="detail-label">IP Address</span>
                            <span class="detail-value">${device.ip}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Signal</span>
                            <div class="signal-strength">
                                <div class="signal-bars">${getSignalBars(device.signalStrength)}</div>
                                <span class="detail-value">${device.signalStrength}/5</span>
                            </div>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Battery</span>
                            <span class="detail-value">${device.battery}%</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Last Seen</span>
                            <span class="detail-value">${device.lastSeen}</span>
                        </div>
                    </div>
                    
                    <div class="device-actions">
                        ${device.status === 'connected' ? 
                            '<button class="btn btn-small btn-secondary">Disconnect</button>' :
                            device.status === 'available' ?
                            '<button class="btn btn-small btn-connect">Connect</button>' :
                            '<button class="btn btn-small btn-secondary" disabled>Busy</button>'
                        }
                        <button class="btn btn-small btn-info">Details</button>
                    </div>
                </div>
            `).join('');
            
            // Add event listeners to device cards
            addDeviceEventListeners();
        }

        function addDeviceEventListeners() {
            document.querySelectorAll('.device-card').forEach(card => {
                const connectBtn = card.querySelector('.btn-connect');
                const detailsBtn = card.querySelector('.btn-info');
                
                if (connectBtn) {
                    connectBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        const deviceId = card.dataset.deviceId;
                        connectToDevice(deviceId);
                    });
                }
                
                if (detailsBtn) {
                    detailsBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        const deviceId = card.dataset.deviceId;
                        showDeviceDetails(deviceId);
                    });
                }
            });
        }

        function connectToDevice(deviceId) {
            const device = devices.find(d => d.id == deviceId);
            if (!device) return;
            
            const card = document.querySelector(`[data-device-id="${deviceId}"]`);
            const connectBtn = card.querySelector('.btn-connect');
            
            connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting...';
            connectBtn.disabled = true;
            
            setTimeout(() => {
                device.status = 'connected';
                device.lastSeen = 'Connected';
                renderDevices();
                alert(`Successfully connected to ${device.name}!`);
            }, 2000);
        }

        function showDeviceDetails(deviceId) {
            const device = devices.find(d => d.id == deviceId);
            if (!device) return;
            
            alert(`Device Details:\n\nName: ${device.name}\nModel: ${device.model}\nIP: ${device.ip}\nStatus: ${device.status}\nSignal: ${device.signalStrength}/5\nBattery: ${device.battery}%\nModes: ${device.modes.join(', ')}`);
        }

        function startScan() {
            if (isScanning) return;
            
            isScanning = true;
            const scanBtn = document.getElementById('scan-btn');
            const scanStatus = document.getElementById('scan-status');
            const scanText = document.getElementById('scan-text');
            
            scanBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scanning...';
            scanBtn.disabled = true;
            scanStatus.style.display = 'block';
            scanStatus.classList.add('scanning');
            
            let progress = 0;
            const scanInterval = setInterval(() => {
                progress += 20;
                scanText.textContent = `Scanning for devices... ${progress}%`;
                
                if (progress >= 100) {
                    clearInterval(scanInterval);
                    setTimeout(() => {
                        scanStatus.style.display = 'none';
                        scanStatus.classList.remove('scanning');
                        scanBtn.innerHTML = '<i class="fas fa-search"></i> Scan for Devices';
                        scanBtn.disabled = false;
                        isScanning = false;
                        renderDevices();
                    }, 500);
                }
            }, 400);
        }

        // Event listeners
        document.getElementById('scan-btn').addEventListener('click', startScan);

        document.getElementById('refresh-btn').addEventListener('click', () => {
            renderDevices();
        });

        document.getElementById('manual-connect-btn').addEventListener('click', () => {
            const ip = prompt('Enter device IP address:');
            if (ip) {
                alert(`Attempting to connect to ${ip}...`);
            }
        });

        // Filter buttons
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                currentFilter = this.dataset.filter;
                renderDevices();
            });
        });

        // Initial render
        renderDevices();
    </script>
</body>
</html>
